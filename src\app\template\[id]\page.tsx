"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON>ertCircle, ArrowLeft, FileText } from "lucide-react";
import html2canvas from "html2canvas";

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
  username?: string;
}

export default function TemplatePreviewPage() {
  const params = useParams();
  const router = useRouter();
  const [template, setTemplate] = useState<Template | null>(null);
  const [screenshotUrl, setScreenshotUrl] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");

  const templateId = params.id as string;

  // Fetch template data and generate screenshot
  useEffect(() => {
    if (templateId) {
      fetchTemplate();
    }
  }, [templateId]);

  const fetchTemplate = async () => {
    try {
      setLoading(true);
      setError("");

      const response = await fetch(`/api/templates/${templateId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch template");
      }

      const templateData: Template = await response.json();
      setTemplate(templateData);

      // Generate screenshot after template is loaded
      await generateScreenshot(templateData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load template";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const generateScreenshot = async (templateData: Template) => {
    try {
      // Create a hidden iframe to load the template
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.width = "1200px";
      iframe.style.height = "1600px";
      iframe.style.border = "none";

      document.body.appendChild(iframe);

      // Load template HTML in iframe
      const templateUrl = `/api/templates/${templateData.id}/view`;
      iframe.src = templateUrl;

      // Wait for iframe to load
      await new Promise((resolve, reject) => {
        iframe.onload = resolve;
        iframe.onerror = reject;
        setTimeout(reject, 10000); // 10 second timeout
      });

      // Wait a bit more for content to render
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Capture screenshot using html2canvas
      const canvas = await html2canvas(iframe.contentDocument!.body, {
        width: 1200,
        height: 1600,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
      });

      // Convert to blob URL
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setScreenshotUrl(url);
        } else {
          throw new Error("Failed to generate screenshot");
        }
      }, "image/png");

      // Clean up iframe
      document.body.removeChild(iframe);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to generate preview";
      setError(errorMessage);
    }
  };

  const handleApplyForCertificate = () => {
    // TODO: Implement certificate application logic
    console.log("Apply for certificate:", template?.template_name);
    // For now, just show an alert
    alert(
      `Application for ${template?.template_name} certificate will be implemented soon.`
    );
  };

  const handleGoBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center gap-4 mb-6">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-8 w-64" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Skeleton className="h-[800px] w-full rounded-lg" />
            </div>
            <div className="space-y-4">
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center gap-4 mb-6">
            <Button variant="outline" onClick={handleGoBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center gap-4 mb-6">
            <Button variant="outline" onClick={handleGoBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>Template not found</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex items-center gap-2">
            <FileText className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">{template.template_name}</h1>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Template Preview */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg border shadow-sm p-6">
              <h2 className="text-lg font-semibold mb-4">Template Preview</h2>

              {screenshotUrl ? (
                <div className="flex justify-center">
                  <img
                    src={screenshotUrl}
                    alt={`Preview of ${template.template_name}`}
                    className="max-w-full h-auto rounded-lg shadow-lg border"
                    style={{ maxHeight: "800px" }}
                  />
                </div>
              ) : (
                <div className="space-y-4">
                  <Skeleton className="h-[600px] w-full rounded-lg" />
                  <div className="text-center text-sm text-muted-foreground">
                    Generating preview...
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Template Information & Actions */}
          <div className="space-y-6">
            {/* Template Details */}
            <div className="bg-white rounded-lg border shadow-sm p-6">
              <h3 className="text-lg font-semibold mb-4">Template Details</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Description
                  </label>
                  <p className="text-sm">
                    {template.description || "No description available"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Layout Size
                  </label>
                  <p className="text-sm">
                    {template.layout_size || "Not specified"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Uploaded By
                  </label>
                  <p className="text-sm">{template.username || "Unknown"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Upload Date
                  </label>
                  <p className="text-sm">
                    {new Date(template.uploaded_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <Button
                onClick={handleApplyForCertificate}
                className="w-full"
                size="lg"
              >
                Apply for Certificate
              </Button>
              <Button
                variant="outline"
                onClick={handleGoBack}
                className="w-full"
                size="lg"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
